import requests
from bs4 import BeautifulSoup
import time
import json
import re
import argparse
from datetime import datetime
import csv
import os

def scrape_website(base_url):
    """Generic website scraper that can handle any URL"""
    all_content = []
    page = 0

    while True:
        url = f"{base_url}?page={page}"
        print(f"Scraping page {page}: {url}")
        try:
            response = requests.get(url, headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                             'AppleWebKit/537.36 (KHTML, like Gecko) '
                             'Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;'
                          'q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            })

            if response.status_code != 200:
                print(f"Failed to fetch page {page}: {response.status_code}")
                break

            soup = BeautifulSoup(response.content, 'html.parser')

            # Look for feed-item articles - this is the main container for each post
            feed_items = soup.find_all('article', class_=lambda x: x and 'feed-item' in x)

            if not feed_items:
                print(f"No feed items found on page {page}, stopping")
                break

            print(f"Found {len(feed_items)} posts on page {page}")

            for item in feed_items:
                content = extract_post_content(item, url, page, base_url)
                if content:
                    all_content.append(content)

            time.sleep(1)  # Be respectful
            page += 1

            # Safety limit
            if page > 100:
                print("Reached page limit")
                break

        except Exception as e:
            print(f"Error on page {page}: {e}")
            break

    return all_content

def extract_post_content(feed_item, url, page, base_url):
    """Extract content from a feed-item element"""
    try:
        content = {}

        # Post type (discussion, careers, article, etc.)
        type_elem = feed_item.find('div', class_='feed-item__type')
        content['type'] = type_elem.text.strip().upper() if type_elem else 'UNKNOWN'

        # Title and post URL
        title_elem = feed_item.find('h4')
        if title_elem:
            title_link = title_elem.find('a')
            content['title'] = title_link.text.strip() if title_link else title_elem.text.strip()
            # Handle relative URLs by adding base domain if needed
            if title_link and title_link.get('href'):
                href = title_link.get('href')
                if href.startswith('/'):
                    # Extract domain from base_url for relative links
                    from urllib.parse import urlparse
                    parsed_url = urlparse(base_url)
                    content['post_url'] = f"{parsed_url.scheme}://{parsed_url.netloc}{href}"
                else:
                    content['post_url'] = href
            else:
                content['post_url'] = ''
        else:
            content['title'] = ''
            content['post_url'] = ''

        # Author information
        user_card = feed_item.find('div', class_='user__super_condensed')
        if user_card:
            author_link = user_card.find('a', href=lambda x: x and '/user/' in x if x else False)
            if author_link and author_link.text.strip():
                content['author'] = author_link.text.strip()
                # Handle relative URLs for author profile
                href = author_link.get('href')
                if href and href.startswith('/'):
                    from urllib.parse import urlparse
                    parsed_url = urlparse(base_url)
                    content['author_profile'] = f"{parsed_url.scheme}://{parsed_url.netloc}{href}"
                else:
                    content['author_profile'] = href or ''
            else:
                # Try to find the link with text content
                author_links = user_card.find_all('a',
                                                href=lambda x: x and '/user/' in x if x else False)
                for link in author_links:
                    if link.text.strip():
                        content['author'] = link.text.strip()
                        href = link.get('href')
                        if href and href.startswith('/'):
                            from urllib.parse import urlparse
                            parsed_url = urlparse(base_url)
                            content['author_profile'] = f"{parsed_url.scheme}://{parsed_url.netloc}{href}"
                        else:
                            content['author_profile'] = href or ''
                        break
                else:
                    content['author'] = 'Unknown'
                    content['author_profile'] = ''
        else:
            content['author'] = 'Unknown'
            content['author_profile'] = ''

        # Date
        date_elem = feed_item.find(attrs={'data-time-display-timestamp': True})
        content['timestamp'] = date_elem.get('data-time-display-timestamp') if date_elem else ''
        content['date_formatted'] = date_elem.text.strip() if date_elem else ''

        # Content preview
        original_wrapper = feed_item.find('div', class_='feed-item__original')
        if original_wrapper:
            # Remove any nested HTML and get clean text
            content['preview'] = original_wrapper.get_text(strip=True)
        else:
            content['preview'] = ''

        # Comment count
        comment_elem = feed_item.find('i', class_=lambda x: x and 'fa-comment' in ' '.join(x) if x else False)
        if comment_elem:
            comment_span = comment_elem.find_next_sibling('span')
            content['comments'] = comment_span.text.strip() if comment_span else '0'
        else:
            content['comments'] = '0'

        # Likes count
        likes_elem = feed_item.find('span', id=lambda x: x and 'totalLikes' in x if x else False)
        content['likes'] = likes_elem.text.strip() if likes_elem else '0'

        # Emoji reactions
        emoji_container = feed_item.find('div', class_=lambda x: x and 'emoji__container' in ' '.join(x) if x else False)
        reactions = []
        if emoji_container:
            emoji_buttons = emoji_container.find_all('button', attrs={'data-emoji': True})
            for button in emoji_buttons:
                emoji = button.get('data-emoji', '')
                count_elem = button.find('span', class_='emoji__count')
                if emoji and count_elem:
                    reactions.append(f"{emoji}:{count_elem.text.strip()}")
        content['reactions'] = ', '.join(reactions) if reactions else ''

        # Extract comments if present
        comments = []
        comment_articles = feed_item.find_all('article', class_='discussion_reply')
        for comment_article in comment_articles:
            comment_data = extract_comment(comment_article)
            if comment_data:
                comments.append(comment_data)

        content['comment_details'] = comments
        content['comment_count_extracted'] = len(comments)

        # Metadata
        content['source_url'] = url
        content['page'] = page

        # Only return if we found meaningful content
        if content['title'] or content['preview']:
            return content
        else:
            return None

    except Exception as e:
        print(f"Error extracting content from feed item: {e}")
        return None

def extract_comment(comment_article):
    """Extract individual comment data"""
    try:
        comment = {}

        # Comment author
        user_elem = comment_article.find('div', class_='user__super_condensed')
        if user_elem:
            author_links = user_elem.find_all('a')
            for link in author_links:
                if link.text.strip():
                    comment['author'] = link.text.strip()
                    break
            else:
                comment['author'] = 'Unknown'
        else:
            comment['author'] = 'Unknown'

        # Comment date
        date_elem = comment_article.find(attrs={'data-time-display-timestamp': True})
        comment['timestamp'] = date_elem.get('data-time-display-timestamp') if date_elem else ''
        comment['date'] = date_elem.text.strip() if date_elem else ''

        # Comment content
        body_elem = comment_article.find('div', class_='comment__body-text')
        comment['content'] = body_elem.get_text(strip=True) if body_elem else ''

        return comment if comment.get('content') else None

    except Exception as e:
        print(f"Error extracting comment: {e}")
        return None

def save_results(data, filename_base, output_formats):
    """Save results in specified formats"""
    saved_files = []

    # JSON format (full data) - always saved as it's the default
    json_filename = f"{filename_base}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    saved_files.append(json_filename)

    # CSV format (main posts only, simplified)
    if 'csv' in output_formats:
        csv_data = []
        for post in data:
            csv_row = {
                'title': post['title'],
                'author': post['author'],
                'date': post['date_formatted'],
                'type': post['type'],
                'preview': post['preview'][:200] + '...' if len(post['preview']) > 200 else post['preview'],
                'comments': post['comments'],
                'likes': post['likes'],
                'reactions': post['reactions'],
                'url': post['post_url']
            }
            csv_data.append(csv_row)

        csv_filename = f"{filename_base}.csv"
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            if csv_data:
                writer = csv.DictWriter(f, fieldnames=csv_data[0].keys())
                writer.writeheader()
                writer.writerows(csv_data)
        saved_files.append(csv_filename)

    # Text format for easy reading
    if 'txt' in output_formats:
        txt_filename = f"{filename_base}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            for i, post in enumerate(data, 1):
                f.write(f"=== POST {i} ===\n")
                f.write(f"Title: {post['title']}\n")
                f.write(f"Type: {post['type']}\n")
                f.write(f"Author: {post['author']}\n")
                f.write(f"Date: {post['date_formatted']}\n")
                f.write(f"Comments: {post['comments']} | Likes: {post['likes']}\n")
                if post['reactions']:
                    f.write(f"Reactions: {post['reactions']}\n")
                f.write(f"URL: {post['post_url']}\n")
                preview_text = post['preview'][:300]
                if len(post['preview']) > 300:
                    preview_text += '...'
                f.write(f"Preview: {preview_text}\n")

                if post['comment_details']:
                    f.write(f"\n--- COMMENTS ({len(post['comment_details'])}) ---\n")
                    # Show first 3 comments
                    for j, comment in enumerate(post['comment_details'][:3], 1):
                        f.write(f"Comment {j} by {comment['author']} ({comment['date']}):\n")
                        comment_text = comment['content'][:150]
                        if len(comment['content']) > 150:
                            comment_text += '...'
                        f.write(f"{comment_text}\n\n")

                f.write("\n" + "="*80 + "\n\n")
        saved_files.append(txt_filename)

    return saved_files

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Generic website scraper')
    parser.add_argument('url', help='Base URL to scrape')
    parser.add_argument('-o', '--output',
                       default=datetime.now().strftime('%Y%m%d'),
                       help='Output filename base (default: current date YYYYMMDD)')
    parser.add_argument('--csv', action='store_true',
                       help='Also output CSV format')
    parser.add_argument('--txt', action='store_true',
                       help='Also output text format')
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_arguments()

    print(f"Starting website scraper for: {args.url}")
    data = scrape_website(args.url)

    print(f"\nScraping complete! Found {len(data)} posts total.")

    if data:
        # Determine output formats
        output_formats = []
        if args.csv:
            output_formats.append('csv')
        if args.txt:
            output_formats.append('txt')

        saved_files = save_results(data, args.output, output_formats)
        print("Data saved to:")
        for filename in saved_files:
            print(f"- {filename}")

        # Quick stats
        types = {}
        for post in data:
            post_type = post['type']
            types[post_type] = types.get(post_type, 0) + 1

        print(f"\nPost types found:")
        for post_type, count in types.items():
            print(f"- {post_type}: {count}")

    else:
        print("No data found - check the selectors or site structure")
