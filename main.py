import requests
from bs4 import BeautifulSoup
import time
import json
import re

def scrape_wildlabs_drones():
    base_url = "https://wildlabs.net/groups/drones"
    all_content = []
    page = 0
    
    while True:
        url = f"{base_url}?page={page}"
        print(f"Scraping page {page}: {url}")
        
        try:
            response = requests.get(url, headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            })
            
            if response.status_code != 200:
                print(f"Failed to fetch page {page}: {response.status_code}")
                break
                
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for feed-item articles - this is the main container for each post
            feed_items = soup.find_all('article', class_=lambda x: x and 'feed-item' in x)
            
            if not feed_items:
                print(f"No feed items found on page {page}, stopping")
                break
            
            print(f"Found {len(feed_items)} posts on page {page}")
            
            for item in feed_items:
                content = extract_post_content(item, url, page)
                if content:
                    all_content.append(content)
            
            time.sleep(1)  # Be respectful
            page += 1
            
            # Safety limit
            if page > 100:
                print("Reached page limit")
                break
            
        except Exception as e:
            print(f"Error on page {page}: {e}")
            break
    
    return all_content

def extract_post_content(feed_item, url, page):
    """Extract content from a feed-item element"""
    try:
        content = {}
        
        # Post type (discussion, careers, article, etc.)
        type_elem = feed_item.find('div', class_='feed-item__type')
        content['type'] = type_elem.text.strip().upper() if type_elem else 'UNKNOWN'
        
        # Title and post URL
        title_elem = feed_item.find('h4')
        if title_elem:
            title_link = title_elem.find('a')
            content['title'] = title_link.text.strip() if title_link else title_elem.text.strip()
            content['post_url'] = 'https://wildlabs.net' + title_link.get('href') if title_link and title_link.get('href') else ''
        else:
            content['title'] = ''
            content['post_url'] = ''
        
        # Author information
        user_card = feed_item.find('div', class_='user__super_condensed')
        if user_card:
            author_link = user_card.find('a', href=lambda x: x and '/user/' in x if x else False)
            if author_link and author_link.text.strip():
                content['author'] = author_link.text.strip()
                content['author_profile'] = 'https://wildlabs.net' + author_link.get('href')
            else:
                # Try to find the link with text content
                author_links = user_card.find_all('a', href=lambda x: x and '/user/' in x if x else False)
                for link in author_links:
                    if link.text.strip():
                        content['author'] = link.text.strip()
                        content['author_profile'] = 'https://wildlabs.net' + link.get('href')
                        break
                else:
                    content['author'] = 'Unknown'
                    content['author_profile'] = ''
        else:
            content['author'] = 'Unknown'
            content['author_profile'] = ''
        
        # Date
        date_elem = feed_item.find(attrs={'data-time-display-timestamp': True})
        content['timestamp'] = date_elem.get('data-time-display-timestamp') if date_elem else ''
        content['date_formatted'] = date_elem.text.strip() if date_elem else ''
        
        # Content preview
        original_wrapper = feed_item.find('div', class_='feed-item__original')
        if original_wrapper:
            # Remove any nested HTML and get clean text
            content['preview'] = original_wrapper.get_text(strip=True)
        else:
            content['preview'] = ''
        
        # Comment count
        comment_elem = feed_item.find('i', class_=lambda x: x and 'fa-comment' in ' '.join(x) if x else False)
        if comment_elem:
            comment_span = comment_elem.find_next_sibling('span')
            content['comments'] = comment_span.text.strip() if comment_span else '0'
        else:
            content['comments'] = '0'
        
        # Likes count
        likes_elem = feed_item.find('span', id=lambda x: x and 'totalLikes' in x if x else False)
        content['likes'] = likes_elem.text.strip() if likes_elem else '0'
        
        # Emoji reactions
        emoji_container = feed_item.find('div', class_=lambda x: x and 'emoji__container' in ' '.join(x) if x else False)
        reactions = []
        if emoji_container:
            emoji_buttons = emoji_container.find_all('button', attrs={'data-emoji': True})
            for button in emoji_buttons:
                emoji = button.get('data-emoji', '')
                count_elem = button.find('span', class_='emoji__count')
                if emoji and count_elem:
                    reactions.append(f"{emoji}:{count_elem.text.strip()}")
        content['reactions'] = ', '.join(reactions) if reactions else ''
        
        # Extract comments if present
        comments = []
        comment_articles = feed_item.find_all('article', class_='discussion_reply')
        for comment_article in comment_articles:
            comment_data = extract_comment(comment_article)
            if comment_data:
                comments.append(comment_data)
        
        content['comment_details'] = comments
        content['comment_count_extracted'] = len(comments)
        
        # Metadata
        content['source_url'] = url
        content['page'] = page
        
        # Only return if we found meaningful content
        if content['title'] or content['preview']:
            return content
        else:
            return None
            
    except Exception as e:
        print(f"Error extracting content from feed item: {e}")
        return None

def extract_comment(comment_article):
    """Extract individual comment data"""
    try:
        comment = {}
        
        # Comment author
        user_elem = comment_article.find('div', class_='user__super_condensed')
        if user_elem:
            author_links = user_elem.find_all('a')
            for link in author_links:
                if link.text.strip():
                    comment['author'] = link.text.strip()
                    break
            else:
                comment['author'] = 'Unknown'
        else:
            comment['author'] = 'Unknown'
        
        # Comment date
        date_elem = comment_article.find(attrs={'data-time-display-timestamp': True})
        comment['timestamp'] = date_elem.get('data-time-display-timestamp') if date_elem else ''
        comment['date'] = date_elem.text.strip() if date_elem else ''
        
        # Comment content
        body_elem = comment_article.find('div', class_='comment__body-text')
        comment['content'] = body_elem.get_text(strip=True) if body_elem else ''
        
        return comment if comment.get('content') else None
        
    except Exception as e:
        print(f"Error extracting comment: {e}")
        return None

def save_results(data):
    """Save results in multiple formats"""
    
    # JSON format (full data)
    with open('wildlabs_drone_posts.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    # CSV format (main posts only, simplified)
    import csv
    csv_data = []
    for post in data:
        csv_row = {
            'title': post['title'],
            'author': post['author'],
            'date': post['date_formatted'],
            'type': post['type'],
            'preview': post['preview'][:200] + '...' if len(post['preview']) > 200 else post['preview'],
            'comments': post['comments'],
            'likes': post['likes'],
            'reactions': post['reactions'],
            'url': post['post_url']
        }
        csv_data.append(csv_row)
    
    with open('wildlabs_drone_posts.csv', 'w', newline='', encoding='utf-8') as f:
        if csv_data:
            writer = csv.DictWriter(f, fieldnames=csv_data[0].keys())
            writer.writeheader()
            writer.writerows(csv_data)
    
    # Text format for easy reading
    with open('wildlabs_drone_posts.txt', 'w', encoding='utf-8') as f:
        for i, post in enumerate(data, 1):
            f.write(f"=== POST {i} ===\n")
            f.write(f"Title: {post['title']}\n")
            f.write(f"Type: {post['type']}\n")
            f.write(f"Author: {post['author']}\n")
            f.write(f"Date: {post['date_formatted']}\n")
            f.write(f"Comments: {post['comments']} | Likes: {post['likes']}\n")
            if post['reactions']:
                f.write(f"Reactions: {post['reactions']}\n")
            f.write(f"URL: {post['post_url']}\n")
            f.write(f"Preview: {post['preview'][:300]}{'...' if len(post['preview']) > 300 else ''}\n")
            
            if post['comment_details']:
                f.write(f"\n--- COMMENTS ({len(post['comment_details'])}) ---\n")
                for j, comment in enumerate(post['comment_details'][:3], 1):  # Show first 3 comments
                    f.write(f"Comment {j} by {comment['author']} ({comment['date']}):\n")
                    f.write(f"{comment['content'][:150]}{'...' if len(comment['content']) > 150 else ''}\n\n")
            
            f.write("\n" + "="*80 + "\n\n")

if __name__ == "__main__":
    print("Starting WildLabs drone group scraper...")
    data = scrape_wildlabs_drones()
    
    print(f"\nScraping complete! Found {len(data)} posts total.")
    
    if data:
        save_results(data)
        print("Data saved to:")
        print("- wildlabs_drone_posts.json (full data)")
        print("- wildlabs_drone_posts.csv (simplified)")
        print("- wildlabs_drone_posts.txt (readable format)")
        
        # Quick stats
        types = {}
        for post in data:
            post_type = post['type']
            types[post_type] = types.get(post_type, 0) + 1
        
        print(f"\nPost types found:")
        for post_type, count in types.items():
            print(f"- {post_type}: {count}")
            
    else:
        print("No data found - check the selectors or site structure")