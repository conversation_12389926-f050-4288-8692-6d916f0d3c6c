# Compact JSON filter to transform scraped post data into a simplified format
# Takes an array of post objects and creates a more concise representation

[
  # Iterate through each post in the input array
  .[] | {
    # Keep the post type (e.g., "DISCUSSION", "ARTICLE", etc.)
    type: .type,

    # Keep the post title
    title: .title,

    # Keep the author name
    author: .author,

    # Use the formatted date instead of timestamp
    date: .date_formatted,

    # Rename 'preview' to 'desc' for brevity
    desc: .preview,

    # Convert likes to number, defaulting to 0 if conversion fails or value is null
    likes: (.likes | tonumber? // 0),

    # Transform comment details into a compact string array
    # Each comment becomes: "Author (Date): Content"
    # The []? operator safely handles cases where comment_details might be null/missing
    comments: [.comment_details[]? | "\(.author) (\(.date)): \(.content)"]
  }
]
